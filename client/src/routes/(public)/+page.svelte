<script lang="ts">
	import UpdateStarsIcon from "$lib/components/icons/ui/UpdateStarsIcon.svelte";
	import { Button } from "$lib/components/ui/button";
	import SectionHeader from "$lib/components/ui/section-header/SectionHeader.svelte";
	import { Accordion } from "bits-ui";

	const items = [
		{
			value: "1",
			title: "What is the meaning of life?",
			content:
				"To become a better person, to help others, and to leave the world a better place than you found it."
		},
		{
			value: "2",
			title: "How do I become a better person?",
			content: "Read books, listen to podcasts, and surround yourself with people who inspire you."
		},
		{
			value: "3",
			title: "What is the best way to help others?",
			content: "Give them your time, attention, and love."
		}
	];
</script>

<div class="page-spacing">
	<section class="section-spacing">
		<!-- Hero Section -->
		<div class="lg:px-18 flex w-full flex-col justify-center gap-4 py-6 md:px-8">
			<div class="flex flex-col gap-4">
				<a
					class="md:text-smlg subsection-subitem-text flex h-fit w-fit items-center justify-start gap-1 rounded-lg border px-2 py-1 md:gap-2"
					href="/changelog"
				>
					<UpdateStarsIcon class="md:size-5.5 size-5" />
					<h6 class="tracking-tight">Version 1.0 is live</h6>
				</a>

				<div class="mx-auto w-full">
					<div class="flex flex-col justify-between gap-8 md:flex-row md:items-center">
						<h1 class="headline-text md:max-w-full">
							Smart skill tracking for future-ready institutions.
						</h1>
						<div class="flex flex-col gap-5 md:max-w-[35%]">
							<p class="page-text">
								Evoprof is your trusted partner for managing student skill profiles, institutional
								reporting, and secure data sharing — powered by AI and blockchain.
							</p>
							<div class="flex gap-4">
								<Button href="/contact-sales">Contact Sales</Button>
								<Button variant="outline" href="/pricing">Explore Plans</Button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- FAQ Section -->
	<section class="section-spacing flex flex-col gap-8">
		<SectionHeader
			sectionName="FAQ"
			headline="Here are the answers to your questions"
			subheadline="<div class='flex flex-col gap-1'><p>After reading this section, if you still have questions,</p><p>feel free to <a href='/contact' class='text-primary decoration-1 underline'>reach out</a> to us.</p></div>"
			color="#00AD09"
		/>

		<Accordion.Root class="w-full sm:max-w-[70%]" type="multiple">
			{#each items as item (item.value)}
				<Accordion.Item value={item.value} class="border-dark-10 group border-b px-1.5">
					<Accordion.Header>
						<Accordion.Trigger
							class="flex w-full flex-1 select-none items-center justify-between py-5 text-[15px] font-medium transition-all [&[data-state=open]>span>svg]:rotate-180"
							disabled={false}
							
						>
							<span class="w-full text-left">{item.title}</span>
							<span
								class="hover:bg-dark-10 inline-flex size-8 items-center justify-center rounded-[7px] bg-transparent"
							>
								<!-- Add an icon or placeholder here if needed -->
							</span>
						</Accordion.Trigger>
					</Accordion.Header>
					<Accordion.Content
						class="data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm tracking-[-0.01em]"
					>
						<div class="pb-[25px]">{item.content}</div>
					</Accordion.Content>
				</Accordion.Item>
			{/each}
		</Accordion.Root>
	</section>
</div>
